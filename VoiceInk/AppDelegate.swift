import Cocoa
import SwiftUI

class AppDelegate: NSObject, NSApplicationDelegate {
    func applicationDidFinishLaunching(_ notification: Notification) {
        updateActivationPolicy()
    }

    func applicationShouldHandleReopen(_ sender: NSApplication, hasVisibleWindows flag: Bool) -> <PERSON><PERSON> {
        updateActivationPolicy()
        
        if !flag {
            createMainWindowIfNeeded()
        }
        return true
    }
    
    func applicationDidBecomeActive(_ notification: Notification) {
        updateActivationPolicy()
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return false
    }
    
    private func updateActivationPolicy() {
        let isMenuBarOnly = UserDefaults.standard.bool(forKey: "IsMenuBarOnly")
        if isMenuBarOnly {
            NSApp.setActivationPolicy(.accessory)
        } else {
            NSApp.setActivationPolicy(.regular)
        }
    }
    
    private func createMainWindowIfNeeded() {
        // Check for existing main windows more specifically
        let existingMainWindow = NSApp.windows.first { window in
            window.title == "Jubu-Transcribe" && !window.title.contains("Onboarding")
        }

        if let existingWindow = existingMainWindow {
            existingWindow.makeKeyAndOrderFront(nil)
        } else {
            // Only create new window if no main window exists
            // Note: ContentView creation should be handled by the main app with proper environment objects
            // This is a fallback for when the app is reopened
            print("⚠️ AppDelegate: Creating fallback window - main app window should handle this")
        }
    }
}
