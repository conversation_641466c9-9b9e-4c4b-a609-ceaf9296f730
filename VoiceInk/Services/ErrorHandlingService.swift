import Foundation
import OSLog
import SwiftUI

/// Centralized error handling service for better error management and user experience
@MainActor
class ErrorHandlingService: ObservableObject {
    static let shared = ErrorHandlingService()
    
    private let logger = Logger(subsystem: "com.jubu.JubuTranscribe", category: "ErrorHandling")
    
    @Published var currentError: AppError?
    @Published var isShowingError = false
    
    private init() {}
    
    // MARK: - Error Types
    
    enum AppError: Error, LocalizedError, Identifiable {
        case whisperModelLoadFailed(String)
        case audioRecordingFailed(String)
        case transcriptionFailed(String)
        case aiEnhancementFailed(String)
        case fileSystemError(String)
        case networkError(String)
        case configurationError(String)
        case memoryWarning
        case unknownError(String)
        
        var id: String {
            switch self {
            case .whisperModelLoadFailed: return "whisper_model_load_failed"
            case .audioRecordingFailed: return "audio_recording_failed"
            case .transcriptionFailed: return "transcription_failed"
            case .aiEnhancementFailed: return "ai_enhancement_failed"
            case .fileSystemError: return "file_system_error"
            case .networkError: return "network_error"
            case .configurationError: return "configuration_error"
            case .memoryWarning: return "memory_warning"
            case .unknownError: return "unknown_error"
            }
        }
        
        var errorDescription: String? {
            switch self {
            case .whisperModelLoadFailed(let message):
                return "Failed to load Whisper model: \(message)"
            case .audioRecordingFailed(let message):
                return "Audio recording failed: \(message)"
            case .transcriptionFailed(let message):
                return "Transcription failed: \(message)"
            case .aiEnhancementFailed(let message):
                return "AI enhancement failed: \(message)"
            case .fileSystemError(let message):
                return "File system error: \(message)"
            case .networkError(let message):
                return "Network error: \(message)"
            case .configurationError(let message):
                return "Configuration error: \(message)"
            case .memoryWarning:
                return "Memory usage is high. Consider closing other applications."
            case .unknownError(let message):
                return "An unexpected error occurred: \(message)"
            }
        }
        
        var recoverySuggestion: String? {
            switch self {
            case .whisperModelLoadFailed:
                return "Try downloading the model again or selecting a different model."
            case .audioRecordingFailed:
                return "Check your microphone permissions and try again."
            case .transcriptionFailed:
                return "Ensure the audio file is valid and try again."
            case .aiEnhancementFailed:
                return "Check your API key and internet connection."
            case .fileSystemError:
                return "Check file permissions and available disk space."
            case .networkError:
                return "Check your internet connection and try again."
            case .configurationError:
                return "Review your settings and try again."
            case .memoryWarning:
                return "Close other applications to free up memory."
            case .unknownError:
                return "Please restart the application and try again."
            }
        }
        
        var severity: ErrorSeverity {
            switch self {
            case .memoryWarning:
                return .warning
            case .networkError, .aiEnhancementFailed:
                return .moderate
            case .whisperModelLoadFailed, .audioRecordingFailed, .transcriptionFailed:
                return .high
            case .fileSystemError, .configurationError, .unknownError:
                return .critical
            }
        }
    }
    
    enum ErrorSeverity {
        case warning
        case moderate
        case high
        case critical
        
        var logLevel: OSLogType {
            switch self {
            case .warning: return .info
            case .moderate: return .default
            case .high: return .error
            case .critical: return .fault
            }
        }
    }
    
    // MARK: - Error Handling
    
    func handle(_ error: Error, context: String = "") {
        let appError = convertToAppError(error, context: context)
        handle(appError)
    }
    
    func handle(_ error: AppError) {
        logger.log(level: error.severity.logLevel, "Error: \(error.localizedDescription)")
        
        currentError = error
        isShowingError = true
        
        // Additional handling based on severity
        switch error.severity {
        case .warning:
            // Auto-dismiss warnings after a delay
            Task {
                try? await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
                if currentError?.id == error.id {
                    dismissError()
                }
            }
        case .critical:
            // For critical errors, we might want to trigger additional recovery actions
            logger.critical("Critical error occurred: \(error.localizedDescription)")
        default:
            break
        }
    }
    
    func dismissError() {
        currentError = nil
        isShowingError = false
    }
    
    // MARK: - Error Conversion
    
    private func convertToAppError(_ error: Error, context: String) -> AppError {
        // Convert various error types to AppError
        if let appError = error as? AppError {
            return appError
        }
        
        let errorMessage = error.localizedDescription
        let contextualMessage = context.isEmpty ? errorMessage : "\(context): \(errorMessage)"
        
        // Try to categorize based on error type or message
        if error is URLError {
            return .networkError(contextualMessage)
        }
        
        if errorMessage.contains("model") || errorMessage.contains("whisper") {
            return .whisperModelLoadFailed(contextualMessage)
        }
        
        if errorMessage.contains("audio") || errorMessage.contains("recording") {
            return .audioRecordingFailed(contextualMessage)
        }
        
        if errorMessage.contains("file") || errorMessage.contains("directory") {
            return .fileSystemError(contextualMessage)
        }
        
        return .unknownError(contextualMessage)
    }
    
    // MARK: - Memory Monitoring
    
    func checkMemoryUsage() {
        var memoryInfo = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let result = withUnsafeMutablePointer(to: &memoryInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if result == KERN_SUCCESS {
            let memoryUsage = memoryInfo.resident_size
            let memoryUsageMB = memoryUsage / (1024 * 1024)
            
            logger.info("Current memory usage: \(memoryUsageMB) MB")
            
            // Warn if memory usage is high (over 500MB)
            if memoryUsageMB > 500 {
                handle(.memoryWarning)
            }
        }
    }
}

// MARK: - SwiftUI Integration

struct ErrorAlert: ViewModifier {
    @ObservedObject var errorHandler = ErrorHandlingService.shared
    
    func body(content: Content) -> some View {
        content
            .alert(
                errorHandler.currentError?.errorDescription ?? "Error",
                isPresented: $errorHandler.isShowingError,
                presenting: errorHandler.currentError
            ) { error in
                Button("OK") {
                    errorHandler.dismissError()
                }
            } message: { error in
                if let suggestion = error.recoverySuggestion {
                    Text(suggestion)
                }
            }
    }
}

extension View {
    func withErrorHandling() -> some View {
        modifier(ErrorAlert())
    }
}
