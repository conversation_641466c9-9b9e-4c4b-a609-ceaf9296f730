import Foundation
import OSLog
import SwiftUI

/// Performance monitoring service to track app performance and identify bottlenecks
@MainActor
class PerformanceMonitor: ObservableObject {
    static let shared = PerformanceMonitor()
    
    private let logger = Logger(subsystem: "com.jubu.JubuTranscribe", category: "Performance")
    
    @Published var isMonitoring = false
    @Published var currentMetrics = PerformanceMetrics()
    
    private var startupTime: Date?
    private var operationTimers: [String: Date] = [:]
    private var memoryCheckTimer: Timer?
    
    private init() {
        startupTime = Date()
        logger.info("PerformanceMonitor initialized")
    }
    
    // MARK: - Performance Metrics
    
    struct PerformanceMetrics {
        var appStartupTime: TimeInterval = 0
        var averageTranscriptionTime: TimeInterval = 0
        var averageModelLoadTime: TimeInterval = 0
        var memoryUsageMB: UInt64 = 0
        var cpuUsagePercent: Double = 0
        
        var formattedStartupTime: String {
            String(format: "%.2f seconds", appStartupTime)
        }
        
        var formattedMemoryUsage: String {
            "\(memoryUsageMB) MB"
        }
        
        var formattedCPUUsage: String {
            String(format: "%.1f%%", cpuUsagePercent)
        }
    }
    
    // MARK: - Monitoring Control
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        logger.info("Performance monitoring started")
        
        // Start periodic memory and CPU monitoring
        memoryCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMetrics()
            }
        }
        
        // Initial metrics update
        updateMetrics()
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        memoryCheckTimer?.invalidate()
        memoryCheckTimer = nil
        
        logger.info("Performance monitoring stopped")
    }
    
    // MARK: - Operation Timing
    
    func startOperation(_ name: String) {
        operationTimers[name] = Date()
        logger.debug("Started operation: \(name)")
    }
    
    func endOperation(_ name: String) -> TimeInterval? {
        guard let startTime = operationTimers[name] else {
            logger.warning("No start time found for operation: \(name)")
            return nil
        }
        
        let duration = Date().timeIntervalSince(startTime)
        operationTimers.removeValue(forKey: name)
        
        logger.info("Operation '\(name)' completed in \(String(format: "%.3f", duration)) seconds")
        
        // Update relevant metrics
        updateOperationMetrics(name: name, duration: duration)
        
        return duration
    }
    
    private func updateOperationMetrics(name: String, duration: TimeInterval) {
        switch name {
        case "transcription":
            // Update average transcription time (simple moving average)
            if currentMetrics.averageTranscriptionTime == 0 {
                currentMetrics.averageTranscriptionTime = duration
            } else {
                currentMetrics.averageTranscriptionTime = (currentMetrics.averageTranscriptionTime + duration) / 2
            }
            
        case "model_load":
            // Update average model load time
            if currentMetrics.averageModelLoadTime == 0 {
                currentMetrics.averageModelLoadTime = duration
            } else {
                currentMetrics.averageModelLoadTime = (currentMetrics.averageModelLoadTime + duration) / 2
            }
            
        default:
            break
        }
    }
    
    // MARK: - Startup Tracking
    
    func markAppReady() {
        guard let startupTime = startupTime else { return }
        
        currentMetrics.appStartupTime = Date().timeIntervalSince(startupTime)
        logger.info("App startup completed in \(currentMetrics.formattedStartupTime)")
        
        // Start monitoring after app is ready
        startMonitoring()
    }
    
    // MARK: - System Metrics
    
    private func updateMetrics() {
        updateMemoryUsage()
        updateCPUUsage()
    }
    
    private func updateMemoryUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if result == KERN_SUCCESS {
            currentMetrics.memoryUsageMB = info.resident_size / (1024 * 1024)
            
            // Log warning if memory usage is high
            if currentMetrics.memoryUsageMB > 400 {
                logger.warning("High memory usage detected: \(currentMetrics.memoryUsageMB) MB")
            }
        }
    }
    
    private func updateCPUUsage() {
        // Simplified CPU monitoring - placeholder implementation
        // For production, you'd want to implement proper CPU usage tracking
        currentMetrics.cpuUsagePercent = 0.0
    }
    
    // MARK: - Performance Warnings
    
    func checkPerformanceThresholds() {
        var warnings: [String] = []
        
        if currentMetrics.appStartupTime > 5.0 {
            warnings.append("Slow app startup (\(currentMetrics.formattedStartupTime))")
        }
        
        if currentMetrics.averageTranscriptionTime > 30.0 {
            warnings.append("Slow transcription performance (\(String(format: "%.2f", currentMetrics.averageTranscriptionTime))s avg)")
        }
        
        if currentMetrics.memoryUsageMB > 500 {
            warnings.append("High memory usage (\(currentMetrics.formattedMemoryUsage))")
        }
        
        if !warnings.isEmpty {
            logger.warning("Performance warnings: \(warnings.joined(separator: ", "))")
        }
    }
    
    // MARK: - Cleanup
    
    deinit {
        stopMonitoring()
    }
}

// MARK: - SwiftUI Integration

struct PerformanceOverlay: View {
    @ObservedObject private var monitor = PerformanceMonitor.shared
    @State private var isExpanded = false
    
    var body: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                
                if monitor.isMonitoring {
                    VStack(alignment: .trailing, spacing: 4) {
                        if isExpanded {
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Startup: \(monitor.currentMetrics.formattedStartupTime)")
                                Text("Memory: \(monitor.currentMetrics.formattedMemoryUsage)")
                                if monitor.currentMetrics.averageTranscriptionTime > 0 {
                                    Text("Avg Transcription: \(String(format: "%.2f", monitor.currentMetrics.averageTranscriptionTime))s")
                                }
                            }
                            .font(.system(size: 10, family: .monospaced))
                            .padding(8)
                            .background(Color.black.opacity(0.8))
                            .foregroundColor(.white)
                            .cornerRadius(8)
                        }
                        
                        Button(action: { isExpanded.toggle() }) {
                            Image(systemName: isExpanded ? "chevron.down" : "chevron.up")
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(.plain)
                    }
                }
            }
        }
        .padding()
    }
}

extension View {
    func withPerformanceMonitoring() -> some View {
        self.overlay(PerformanceOverlay())
    }
}
