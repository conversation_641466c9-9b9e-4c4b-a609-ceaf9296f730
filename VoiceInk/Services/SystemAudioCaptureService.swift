import Foundation
import AVFoundation
import Screen<PERSON>aptureKit
import OSLog

@MainActor
class SystemAudioCaptureService: NSObject, ObservableObject {
    static let shared = SystemAudioCaptureService()
    
    private let logger = Logger(subsystem: "com.jubu.JubuTranscribe", category: "SystemAudioCapture")
    
    @Published var isCapturing = false
    @Published var availableApplications: [SCRunningApplication] = []
    @Published var selectedApplication: SCRunningApplication?
    
    private var stream: SCStream?
    private var audioEngine: AVAudioEngine?
    private var audioFile: AVAudioFile?
    private var audioBuffer: AVAudioPCMBuffer?
    
    // Meeting platform detection
    private let meetingPlatforms = [
        "zoom.us",
        "us02web.zoom.us", 
        "us03web.zoom.us",
        "us04web.zoom.us",
        "us05web.zoom.us",
        "meet.google.com",
        "teams.microsoft.com",
        "teams.live.com",
        "webex.com",
        "gotomeeting.com",
        "bluejeans.com",
        "whereby.com",
        "discord.com",
        "slack.com"
    ]
    
    override init() {
        super.init()
        setupAudioEngine()
    }
    
    // MARK: - Public Methods
    
    func requestPermissions() async -> Bool {
        do {
            // Request screen capture permission
            let canRecord = try await SCShareableContent.excludingDesktopWindows(false, onScreenWindowsOnly: true)
            logger.info("Screen capture permission granted")
            return true
        } catch {
            logger.error("Failed to get screen capture permission: \(error.localizedDescription)")
            return false
        }
    }
    
    func refreshAvailableApplications() async {
        do {
            let content = try await SCShareableContent.excludingDesktopWindows(false, onScreenWindowsOnly: true)
            
            // Filter for meeting applications and audio-capable apps
            let meetingApps = content.applications.filter { app in
                guard let bundleId = app.bundleIdentifier, !bundleId.isEmpty else { return false }
                
                // Check for meeting platforms
                let isMeetingApp = meetingPlatforms.contains { platform in
                    bundleId.lowercased().contains(platform.lowercased()) ||
                    app.applicationName.lowercased().contains(platform.lowercased())
                }
                
                // Check for common meeting apps by bundle ID
                let commonMeetingBundles = [
                    "us.zoom.xos",
                    "com.microsoft.teams",
                    "com.google.Chrome", // For web-based meetings
                    "com.apple.Safari",
                    "org.mozilla.firefox",
                    "com.cisco.webexmeetingsapp",
                    "com.gotomeeting.GoToMeetingHD",
                    "com.bluejeans.BlueJeansApp",
                    "com.discord.Discord",
                    "com.tinyspeck.slackmacgap"
                ]
                
                return isMeetingApp || commonMeetingBundles.contains(bundleId)
            }
            
            availableApplications = meetingApps
            logger.info("Found \(meetingApps.count) meeting applications")
            
        } catch {
            logger.error("Failed to get available applications: \(error.localizedDescription)")
        }
    }
    
    func startCapturing(application: SCRunningApplication) async -> Bool {
        guard !isCapturing else { return false }
        
        do {
            let content = try await SCShareableContent.excludingDesktopWindows(false, onScreenWindowsOnly: true)
            
            // Create stream configuration for audio capture
            let config = SCStreamConfiguration()
            config.capturesAudio = true
            config.excludesCurrentProcessAudio = true
            config.sampleRate = 48000
            config.channelCount = 2
            
            // Create content filter for the specific application
            // Create a basic content filter for system audio capture
            // Note: This is a simplified implementation for macOS system audio capture
            guard let display = content.displays.first else {
                logger.error("No displays available for system audio capture")
                return false
            }
            let filter = SCContentFilter(display: display, excludingWindows: [])
            
            // Create and start the stream
            stream = SCStream(filter: filter, configuration: config, delegate: self)
            
            try await stream?.startCapture()
            
            selectedApplication = application
            isCapturing = true
            
            logger.info("Started capturing audio from \(application.applicationName)")
            return true
            
        } catch {
            logger.error("Failed to start audio capture: \(error.localizedDescription)")
            return false
        }
    }
    
    func stopCapturing() async {
        guard isCapturing else { return }
        
        do {
            try await stream?.stopCapture()
            stream = nil
            selectedApplication = nil
            isCapturing = false
            
            logger.info("Stopped audio capture")
        } catch {
            logger.error("Failed to stop audio capture: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Private Methods
    
    private func setupAudioEngine() {
        audioEngine = AVAudioEngine()

        // Note: AVAudioSession is iOS-specific. On macOS, audio configuration
        // is handled differently through AVAudioEngine and system preferences.
        logger.info("Audio engine setup completed for macOS")
    }
    
    @MainActor
    private func processAudioBuffer(_ buffer: CMSampleBuffer) {
        // Convert CMSampleBuffer to AVAudioPCMBuffer for processing
        guard let formatDescription = CMSampleBufferGetFormatDescription(buffer) else { return }

        let audioStreamBasicDescription = CMAudioFormatDescriptionGetStreamBasicDescription(formatDescription)
        guard audioStreamBasicDescription != nil else { return }

        // Process the audio data for transcription
        // This would integrate with the existing WhisperState for real-time transcription
        logger.debug("Processing audio buffer for transcription")
    }
    
    // MARK: - Meeting Platform Detection
    
    func detectActiveMeetingPlatform() -> String? {
        // Check running applications for active meeting platforms
        let workspace = NSWorkspace.shared
        let runningApps = workspace.runningApplications
        
        for app in runningApps {
            guard let bundleId = app.bundleIdentifier else { continue }
            
            // Check for meeting platform bundle IDs
            if bundleId.contains("zoom") {
                return "Zoom"
            } else if bundleId.contains("teams") {
                return "Microsoft Teams"
            } else if bundleId.contains("webex") {
                return "Cisco Webex"
            } else if bundleId.contains("gotomeeting") {
                return "GoToMeeting"
            } else if bundleId.contains("bluejeans") {
                return "BlueJeans"
            } else if bundleId.contains("discord") {
                return "Discord"
            } else if bundleId.contains("slack") {
                return "Slack"
            }
        }
        
        return nil
    }
    
    func isMeetingActive() -> Bool {
        return detectActiveMeetingPlatform() != nil
    }
}

// MARK: - SCStreamDelegate

extension SystemAudioCaptureService: SCStreamDelegate {
    nonisolated func stream(_ stream: SCStream, didOutputSampleBuffer sampleBuffer: CMSampleBuffer, of type: SCStreamOutputType) {
        guard type == .audio else { return }

        // Process audio buffer for transcription
        Task { @MainActor in
            processAudioBuffer(sampleBuffer)
        }
    }
    
    nonisolated func stream(_ stream: SCStream, didStopWithError error: Error) {
        logger.error("Stream stopped with error: \(error.localizedDescription)")
        
        Task { @MainActor in
            isCapturing = false
            selectedApplication = nil
        }
    }
}

// MARK: - Meeting Transcript Model

struct MeetingTranscript {
    let id = UUID()
    let meetingPlatform: String
    let startTime: Date
    var endTime: Date?
    var participants: [String] = []
    var segments: [TranscriptSegment] = []
    
    struct TranscriptSegment {
        let id = UUID()
        let timestamp: Date
        let speaker: String?
        let text: String
        let confidence: Float
    }
    
    var duration: TimeInterval {
        let end = endTime ?? Date()
        return end.timeIntervalSince(startTime)
    }
    
    var fullTranscript: String {
        return segments.map { segment in
            if let speaker = segment.speaker {
                return "[\(speaker)]: \(segment.text)"
            } else {
                return segment.text
            }
        }.joined(separator: "\n")
    }
}
