import Foundation
import SwiftData
import OSLog

/// A service container that manages lazy initialization and dependency injection
/// to improve app startup performance and prevent retain cycles
@MainActor
class ServiceContainer: ObservableObject {
    static let shared = ServiceContainer()
    
    private let logger = Logger(subsystem: "com.jubu.JubuTranscribe", category: "ServiceContainer")
    
    // Lazy service properties
    private var _aiService: AIService?
    private var _enhancementService: AIEnhancementService?
    private var _whisperState: WhisperState?
    private var _hotkeyManager: HotkeyManager?
    private var _menuBarManager: MenuBarManager?
    private var _activeWindowService: ActiveWindowService?
    
    private var modelContext: ModelContext?
    private var container: ModelContainer?
    
    private init() {
        logger.info("ServiceContainer initialized")
    }
    
    // MARK: - Configuration
    
    func configure(with container: ModelContainer) {
        self.container = container
        self.modelContext = container.mainContext
        logger.info("ServiceContainer configured with ModelContainer")
    }
    
    // MARK: - Lazy Service Getters
    
    var aiService: AIService {
        if let service = _aiService {
            return service
        }
        
        logger.info("Initializing AIService")
        let service = AIService()
        _aiService = service
        return service
    }
    
    var enhancementService: AIEnhancementService {
        if let service = _enhancementService {
            return service
        }
        
        guard let modelContext = modelContext else {
            fatalError("ServiceContainer must be configured with ModelContainer before accessing enhancementService")
        }
        
        logger.info("Initializing AIEnhancementService")
        let service = AIEnhancementService(aiService: aiService, modelContext: modelContext)
        _enhancementService = service
        return service
    }
    
    var whisperState: WhisperState {
        if let state = _whisperState {
            return state
        }
        
        guard let modelContext = modelContext else {
            fatalError("ServiceContainer must be configured with ModelContainer before accessing whisperState")
        }
        
        logger.info("Initializing WhisperState")
        let state = WhisperState(modelContext: modelContext, enhancementService: enhancementService)
        _whisperState = state
        return state
    }
    
    var hotkeyManager: HotkeyManager {
        if let manager = _hotkeyManager {
            return manager
        }
        
        logger.info("Initializing HotkeyManager")
        let manager = HotkeyManager(whisperState: whisperState)
        _hotkeyManager = manager
        return manager
    }
    
    var menuBarManager: MenuBarManager {
        if let manager = _menuBarManager {
            return manager
        }
        
        guard let container = container else {
            fatalError("ServiceContainer must be configured with ModelContainer before accessing menuBarManager")
        }
        
        logger.info("Initializing MenuBarManager")
        let updaterViewModel = UpdaterViewModel()
        let manager = MenuBarManager(
            updaterViewModel: updaterViewModel,
            whisperState: whisperState,
            container: container,
            enhancementService: enhancementService,
            aiService: aiService,
            hotkeyManager: hotkeyManager
        )
        _menuBarManager = manager
        return manager
    }
    
    var activeWindowService: ActiveWindowService {
        if let service = _activeWindowService {
            return service
        }
        
        logger.info("Configuring ActiveWindowService")
        let service = ActiveWindowService.shared
        service.configure(with: enhancementService)
        service.configureWhisperState(whisperState)
        _activeWindowService = service
        return service
    }
    
    // MARK: - Cleanup
    
    func cleanup() {
        logger.info("Cleaning up ServiceContainer")
        
        // Clean up services in reverse dependency order
        _menuBarManager = nil
        _hotkeyManager = nil
        _whisperState = nil
        _enhancementService = nil
        _aiService = nil
        _activeWindowService = nil
        
        modelContext = nil
        container = nil
    }
    
    deinit {
        cleanup()
    }
}

// MARK: - Service Access Extensions

extension ServiceContainer {
    /// Pre-warm critical services for faster access
    func preWarmCriticalServices() {
        logger.info("Pre-warming critical services")
        
        // Initialize only the most critical services
        _ = aiService
        _ = enhancementService
        
        // Defer other services to when they're actually needed
        Task {
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            _ = whisperState
            _ = hotkeyManager
        }
    }
    
    /// Check if all services are initialized
    var areAllServicesInitialized: Bool {
        return _aiService != nil &&
               _enhancementService != nil &&
               _whisperState != nil &&
               _hotkeyManager != nil &&
               _menuBarManager != nil &&
               _activeWindowService != nil
    }
}
