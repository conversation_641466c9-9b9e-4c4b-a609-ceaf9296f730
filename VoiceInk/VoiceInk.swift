import SwiftUI
import SwiftData
import Sparkle
import AppKit
import OSLog

@main
struct JubuTranscribeApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    let container: ModelContainer

    @StateObject private var serviceContainer = ServiceContainer.shared
    @StateObject private var updaterViewModel = UpdaterViewModel()
    @AppStorage("hasCompletedOnboarding") private var hasCompletedOnboarding = false

    // Audio cleanup manager for automatic deletion of old audio files
    private let audioCleanupManager = AudioCleanupManager.shared
    
    init() {
        do {
            let schema = Schema([
                Transcription.self
            ])
            
            // Create app-specific Application Support directory URL
            let appSupportURL = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask)[0]
                .appendingPathComponent("com.jubu.JubuTranscribe", isDirectory: true)
            
            // Create the directory if it doesn't exist
            try? FileManager.default.createDirectory(at: appSupportURL, withIntermediateDirectories: true)
            
            // Configure SwiftData to use the conventional location
            let storeURL = appSupportURL.appendingPathComponent("default.store")
            let modelConfiguration = ModelConfiguration(schema: schema, url: storeURL)
            
            container = try ModelContainer(for: schema, configurations: [modelConfiguration])
            
            // Print SwiftData storage location
            if let url = container.mainContext.container.configurations.first?.url {
                print("💾 SwiftData storage location: \(url.path)")
            }
            
        } catch {
            fatalError("Failed to create ModelContainer for Transcription: \(error.localizedDescription)")
        }
        
        // Configure service container for optimized dependency injection
        serviceContainer.configure(with: container)

        // Pre-warm critical services for better startup performance
        serviceContainer.preWarmCriticalServices()
    }
    
    var body: some Scene {
        WindowGroup {
            if hasCompletedOnboarding {
                ContentView()
                    .environmentObject(serviceContainer.whisperState)
                    .environmentObject(serviceContainer.hotkeyManager)
                    .environmentObject(updaterViewModel)
                    .environmentObject(serviceContainer.menuBarManager)
                    .environmentObject(serviceContainer.aiService)
                    .environmentObject(serviceContainer.enhancementService)
                    .modelContainer(container)
                    .onAppear {
                        updaterViewModel.silentlyCheckForUpdates()

                        // Start the automatic audio cleanup process
                        audioCleanupManager.startAutomaticCleanup(modelContext: container.mainContext)

                        // Mark app as ready for performance monitoring
                        PerformanceMonitor.shared.markAppReady()

                        // Start memory monitoring
                        Task {
                            try? await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
                            ErrorHandlingService.shared.checkMemoryUsage()
                        }
                    }
                    .background(WindowAccessor { window in
                        WindowManager.shared.configureWindow(window)
                    })
                    .onDisappear {
                        serviceContainer.whisperState.unloadModel()

                        // Stop the automatic audio cleanup process
                        audioCleanupManager.stopAutomaticCleanup()

                        // Stop performance monitoring
                        PerformanceMonitor.shared.stopMonitoring()

                        // Cleanup service container
                        serviceContainer.cleanup()
                    }
                    .withErrorHandling()
                    .withPerformanceMonitoring()
            } else {
                OnboardingView(hasCompletedOnboarding: $hasCompletedOnboarding)
                    .environmentObject(serviceContainer.hotkeyManager)
                    .environmentObject(serviceContainer.whisperState)
                    .environmentObject(serviceContainer.aiService)
                    .environmentObject(serviceContainer.enhancementService)
                    .frame(minWidth: 880, minHeight: 780)
                    .cornerRadius(16)
                    .clipped()
                    .background(WindowAccessor { window in
                        // Ensure this is called only once or is idempotent
                        if window.title != "Jubu-Transcribe Onboarding" { // Prevent re-configuration
                            WindowManager.shared.configureOnboardingPanel(window)
                        }
                    })
            }
        }
        .commands {
            CommandGroup(after: .appInfo) {
                CheckForUpdatesView(updaterViewModel: updaterViewModel)
            }
        }
        
        MenuBarExtra {
            MenuBarView()
                .environmentObject(serviceContainer.whisperState)
                .environmentObject(serviceContainer.hotkeyManager)
                .environmentObject(serviceContainer.menuBarManager)
                .environmentObject(updaterViewModel)
                .environmentObject(serviceContainer.aiService)
                .environmentObject(serviceContainer.enhancementService)
        } label: {
            let image: NSImage = {
                let ratio = $0.size.height / $0.size.width
                $0.size.height = 22
                $0.size.width = 22 / ratio
                return $0
            }(NSImage(named: "menuBarIcon")!)

            Image(nsImage: image)
        }
        .menuBarExtraStyle(.menu)
        
        #if DEBUG
        WindowGroup("Debug") {
            Button("Toggle Menu Bar Only") {
                serviceContainer.menuBarManager.isMenuBarOnly.toggle()
            }
        }
        #endif
    }
}

class UpdaterViewModel: ObservableObject {
    private let updaterController: SPUStandardUpdaterController
    
    @Published var canCheckForUpdates = false
    
    init() {
        updaterController = SPUStandardUpdaterController(startingUpdater: true, updaterDelegate: nil, userDriverDelegate: nil)
        
        // Enable automatic update checking
        updaterController.updater.automaticallyChecksForUpdates = true
        updaterController.updater.updateCheckInterval = 24 * 60 * 60
        
        updaterController.updater.publisher(for: \.canCheckForUpdates)
            .assign(to: &$canCheckForUpdates)
    }
    
    func checkForUpdates() {
        // This is for manual checks - will show UI
        updaterController.checkForUpdates(nil)
    }
    
    func silentlyCheckForUpdates() {
        // This checks for updates in the background without showing UI unless an update is found
        updaterController.updater.checkForUpdatesInBackground()
    }
}

struct CheckForUpdatesView: View {
    @ObservedObject var updaterViewModel: UpdaterViewModel
    
    var body: some View {
        Button("Check for Updates…", action: updaterViewModel.checkForUpdates)
            .disabled(!updaterViewModel.canCheckForUpdates)
    }
}

struct WindowAccessor: NSViewRepresentable {
    let callback: (NSWindow) -> Void
    
    func makeNSView(context: Context) -> NSView {
        let view = NSView()
        DispatchQueue.main.async {
            if let window = view.window {
                callback(window)
            }
        }
        return view
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {}
}



