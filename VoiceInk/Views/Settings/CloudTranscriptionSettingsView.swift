import SwiftUI

struct CloudTranscriptionSettingsView: View {
    @StateObject private var cloudService = CloudTranscriptionService.shared
    @State private var showingAPIKeyAlert = false
    @State private var selectedProviderForKey: CloudTranscriptionService.CloudProvider?
    @State private var tempAPIKey = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            headerSection
            
            providerSelectionSection
            
            apiKeyManagementSection
            
            settingsSection
            
            statusSection
            
            Spacer()
        }
        .padding()
        .alert("Enter API Key", isPresented: $showingAPIKeyAlert) {
            apiKeyAlert
        }
    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "cloud.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("Cloud Transcription")
                    .font(.title2)
                    .fontWeight(.semibold)
            }
            
            Text("Use cloud-based AI services for transcription with automatic fallback to local processing.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var providerSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Provider")
                .font(.headline)
            
            Picker("Cloud Provider", selection: $cloudService.selectedProvider) {
                ForEach(CloudTranscriptionService.CloudProvider.allCases, id: \.self) { provider in
                    HStack {
                        Text(provider.displayName)
                        
                        if cloudService.isConfigured(for: provider) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                        } else if provider.requiresAPIKey {
                            Image(systemName: "exclamationmark.circle.fill")
                                .foregroundColor(.orange)
                                .font(.caption)
                        }
                    }
                    .tag(provider)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    private var apiKeyManagementSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("API Keys")
                .font(.headline)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(CloudTranscriptionService.CloudProvider.allCases, id: \.self) { provider in
                    apiKeyCard(for: provider)
                }
            }
        }
    }
    
    private func apiKeyCard(for provider: CloudTranscriptionService.CloudProvider) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(provider.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                if cloudService.isConfigured(for: provider) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else if provider.requiresAPIKey {
                    Image(systemName: "exclamationmark.circle.fill")
                        .foregroundColor(.orange)
                }
            }
            
            if provider.requiresAPIKey {
                Button(action: {
                    selectedProviderForKey = provider
                    tempAPIKey = cloudService.getAPIKey(for: provider) ?? ""
                    showingAPIKeyAlert = true
                }) {
                    HStack {
                        Image(systemName: "key.fill")
                        Text(cloudService.isConfigured(for: provider) ? "Update Key" : "Add Key")
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                Text("No API key required")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
    
    private var settingsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Settings")
                .font(.headline)
            
            Toggle("Use cloud as fallback when local transcription fails", isOn: $cloudService.useCloudFallback)
                .toggleStyle(SwitchToggleStyle())
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Request Timeout: \(Int(cloudService.cloudTimeout)) seconds")
                    .font(.subheadline)
                
                Slider(value: $cloudService.cloudTimeout, in: 10...120, step: 5) {
                    Text("Timeout")
                } minimumValueLabel: {
                    Text("10s")
                        .font(.caption)
                } maximumValueLabel: {
                    Text("120s")
                        .font(.caption)
                }
            }
        }
    }
    
    private var statusSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Status")
                .font(.headline)
            
            HStack {
                Circle()
                    .fill(cloudService.isConfigured(for: cloudService.selectedProvider) ? Color.green : Color.orange)
                    .frame(width: 8, height: 8)
                
                Text(cloudService.isConfigured(for: cloudService.selectedProvider) 
                     ? "\(cloudService.selectedProvider.displayName) is ready"
                     : "\(cloudService.selectedProvider.displayName) requires configuration")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            if let error = cloudService.lastError {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                    
                    Text("Last error: \(error.localizedDescription)")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
        }
    }
    
    private var apiKeyAlert: some View {
        Group {
            SecureField("API Key", text: $tempAPIKey)
            
            Button("Cancel") {
                tempAPIKey = ""
                selectedProviderForKey = nil
            }
            
            Button("Save") {
                if let provider = selectedProviderForKey {
                    cloudService.setAPIKey(tempAPIKey, for: provider)
                }
                tempAPIKey = ""
                selectedProviderForKey = nil
            }
            .disabled(tempAPIKey.isEmpty)
        }
    }
}

struct CloudTranscriptionSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        CloudTranscriptionSettingsView()
            .frame(width: 600, height: 500)
    }
}
